# ninja log v6
70	377	7771213348377342	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	ac7edd2f1fa809d8
165	407	7771213349325183	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	1f718452fc212f5d
44	418	7771213348125873	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	e7d977418de16b3
26	432	7771213347948064	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	4f0629051380126c
104	441	7771213348712908	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	7c89f4ca13349123
78	454	7771213348467572	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	2e83016f7125c2f6
121	465	7771213348888077	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	88908eb5fb33ce7a
36	474	7771213348033028	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	aca003aa4566b13a
52	483	7771213348196117	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	5a7eac5c1c09cc87
95	527	7771213348635997	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	743ba3fd0c911355
132	536	7771213348995727	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	b23d6c9fab4fb907
85	545	7771213348537750	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	a4a011e2d9816f80
139	570	7771213349062537	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	2b3bd89de35630e5
60	588	7771213348276442	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	89f36d9401723be
192	597	7771213349605593	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	4b11df6725e9d1ba
157	636	7771213349244673	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	e45e10918050dfb0
174	687	7771213349432870	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	ae5cfc05c5a8fd10
183	721	7771213349504048	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	f6662c89472fb1e
408	779	7771213351764758	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	47c208067be9d651
379	788	7771213351465211	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	af97d10c98fd6215
484	971	7771213352516525	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	e84b56dfae6d94b6
433	1031	7771213352006931	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	f275cb54fd229b45
546	1040	7771213353145328	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	16cdbc0577b5304
537	1048	7771213353049299	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	c3ad8b2ae0f2f761
528	1058	7771213352961453	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	a8bd9c382a66122e
571	1068	7771213353385942	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	2a42468e9b6967fa
455	1076	7771213352220227	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	c964cf0e34d93029
474	1084	7771213352431653	esp-idf/log/liblog.a	9fe540e68b3f479f
465	1097	7771213352334876	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	fc84eedbea8e4208
637	1110	7771213354037284	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	a51da225f72d8a59
588	1121	7771213353567543	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	4ea0fc324b815d8
597	1151	7771213353648297	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	10c2ae2c52d4280f
419	1174	7771213351868850	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	62b9078695bdf7a9
442	1189	7771213352091217	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	f4576ebb998764d7
687	1286	7771213354563015	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	80620778d920c90f
721	1377	7771213354887016	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	2e1da854db8e92a3
971	1387	7771213357388147	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	7d9848c03906a592
1049	1471	7771213358156736	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	b3d5472d26ef9a3
1032	1482	7771213357994301	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	693b1cefdf5675af
780	1492	7771213355467636	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	6fc297156240c2d6
788	1501	7771213355564771	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	3c045548febdeacf
1068	1510	7771213358369062	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	548cd74f6dddaa83
1077	1522	7771213358439501	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	c572c5c7d1787c64
1040	1573	7771213358075933	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	436ab8398ae337cd
1084	1619	7771213358526172	esp-idf/esp_rom/libesp_rom.a	76a5ded7b2ca549c
1175	1628	7771213359434683	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	958a6f0343d3a6ae
1058	1679	7771213358261499	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	d4c198ef93cb0627
1111	1701	7771213358779794	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	dd68be7af830e54b
1378	1776	7771213361456129	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	fb1d783f1fc6999f
1121	1809	7771213358884645	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	8691aaacde663ef1
1387	1858	7771213361552881	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	8c5fa4f5a3433383
1492	1880	7771213362608422	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	531bac33cf71ed00
1471	1899	7771213362392451	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	443b96efb7c6069c
1483	1909	7771213362503593	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	7be12f9a9c15e345
1522	1920	7771213362907066	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	f088d255c3206d2d
1511	1929	7771213362790405	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	d45cd5d41e6f2780
1098	1996	7771213358661984	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	73bad240e72038db
1286	2011	7771213360535577	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	28b9d620aa85dde0
1629	2044	7771213363963482	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	56faded760bb5c6d
1679	2053	7771213364463460	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	4d7101eb17ce0b4e
1619	2076	7771213363857849	esp-idf/esp_common/libesp_common.a	16d17467a2c4e525
1701	2100	7771213364686513	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	3218a7a712f7c9f3
1573	2109	7771213363417203	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	e6322ded129131d8
1151	2118	7771213359192189	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	61276d89f376900b
1190	2134	7771213359587579	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	c8e18c3ff045ec9e
1909	2148	7771213366772495	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	12889012e50b6dec
1920	2160	7771213366888032	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	8117b1abdd8ae5ad
1900	2197	7771213366688737	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	4ece997a782feb60
1776	2207	7771213365438844	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	dc5ff90409846236
1501	2215	7771213362685484	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	18fb62ac85589c67
1929	2224	7771213366965063	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	4c46a4c58ed3afe3
2054	2250	7771213368208530	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	b57f8c2a254bd570
1996	2264	7771213367647614	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	f9302f071792c865
2011	2281	7771213367783245	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	55e4485b45905110
1810	2310	7771213365782938	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	514081a2fae92ff2
2044	2319	7771213368123053	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	391f82e2f04a00c5
2109	2329	7771213368774828	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	8b1a3e9c13088575
1859	2339	7771213366267572	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	977179509d5045cf
2148	2347	7771213369168774	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	c6232ff47fd254ba
2100	2377	7771213368688562	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	728e213496b12ab9
2119	2386	7771213368871013	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	36284a70d4d93c65
2134	2395	7771213369016262	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	1d6d4d64b28f05f5
2198	2423	7771213369658712	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	622b247c7ed7b2f3
2160	2450	7771213369275494	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	79a690756593a85d
2225	2464	7771213369921406	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	cd88ef30ced30729
2215	2465	7771213369841093	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	2f7eee66471fe57
2207	2468	7771213369740677	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	d68e5dc1f90372f1
2251	2475	7771213370185886	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	217868e2020e27fc
2076	2478	7771213368446026	esp-idf/esp_hw_support/libesp_hw_support.a	a22e20a51d2bd2b0
2395	2495	7771213372529955	project_elf_src_esp32s3.c	b8d0ff71e4b9b057
2395	2495	7771213372529955	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/project_elf_src_esp32s3.c	b8d0ff71e4b9b057
2265	2503	7771213370326177	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	f08f3d5dee8b8c0
2310	2505	7771213370775322	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	1e186cd9fac3c7b3
2319	2509	7771213370872330	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	c54abca9198becba
2281	2523	7771213370487918	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	88fd07eda1bf1b0d
2329	2536	7771213370980235	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	2383bf602ff145a4
2339	2537	7771213371073047	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	f9f82b55bf0e4872
2377	2547	7771213371458240	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	cd9fe949a67de97
2347	2571	7771213371152735	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	1c61d55442d51011
2495	2596	7771213372630542	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	f6defcaf21da4b68
2386	2599	7771213371539062	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	8fd2f941b1abe2d7
2423	2632	7771213371903616	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	834482eb125c9057
2478	2635	7771213372468884	esp-idf/esp_system/libesp_system.a	6ba3db5ebc7222d0
2635	2781	7771213374025083	esp-idf/efuse/libefuse.a	b027f75ac9dc6d3a
1881	2793	7771213366485193	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	75d15f451b03cdd4
2781	2967	7771213375497465	esp-idf/bootloader_support/libbootloader_support.a	41abbb3847180a9a
2967	3088	7771213377354606	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	7704240e3fc65260
3088	3203	7771213378569202	esp-idf/spi_flash/libspi_flash.a	f2f1321b4133f01b
3203	3340	7771213379698017	esp-idf/hal/libhal.a	4ecd0b7d65881b5c
3340	3468	7771213381071584	esp-idf/micro-ecc/libmicro-ecc.a	564c426ed7c13bba
3468	3669	7771213382353394	esp-idf/soc/libsoc.a	be819c31531e0d15
3669	3799	7771213384377563	esp-idf/xtensa/libxtensa.a	1e98b69c813b3b67
3799	3924	7771213385668396	esp-idf/main/libmain.a	5d9eeaf9cc3081fe
3924	4177	7771213386909296	bootloader.elf	7788bcbde2f2077
4177	4474	7771213392335508	.bin_timestamp	d1e10164ef537244
4177	4474	7771213392335508	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/.bin_timestamp	d1e10164ef537244
4475	4569	7771213392415567	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
4475	4569	7771213392415567	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
