# ninja log v6
39	202	7771217459368607	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	ac7edd2f1fa809d8
24	236	7771217459214207	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	e7d977418de16b3
20	253	7771217459167165	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	aca003aa4566b13a
70	261	7771217459668860	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	88908eb5fb33ce7a
15	280	7771217459109377	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	4f0629051380126c
29	293	7771217459230539	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	5a7eac5c1c09cc87
57	302	7771217459544810	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	743ba3fd0c911355
99	311	7771217459967629	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	1f718452fc212f5d
63	320	7771217459611347	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	7c89f4ca13349123
77	329	7771217459741272	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	b23d6c9fab4fb907
51	342	7771217459484301	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	a4a011e2d9816f80
45	366	7771217459425751	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	2e83016f7125c2f6
84	442	7771217459800750	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	2b3bd89de35630e5
34	465	7771217459289537	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	89f36d9401723be
91	478	7771217459881969	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	e45e10918050dfb0
122	488	7771217460194299	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	4b11df6725e9d1ba
204	505	7771217461017615	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	af97d10c98fd6215
108	534	7771217460054137	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	ae5cfc05c5a8fd10
236	551	7771217461336711	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	47c208067be9d651
115	582	7771217460126496	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	f6662c89472fb1e
281	622	7771217461784432	esp-idf/log/liblog.a	9fe540e68b3f479f
321	669	7771217462179572	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	e84b56dfae6d94b6
329	716	7771217462262402	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	a8bd9c382a66122e
343	739	7771217462401870	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	c3ad8b2ae0f2f761
262	752	7771217461588914	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	f275cb54fd229b45
367	762	7771217462642331	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	16cdbc0577b5304
302	775	7771217461997621	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	c964cf0e34d93029
311	851	7771217462087178	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	fc84eedbea8e4208
443	879	7771217463408150	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	2a42468e9b6967fa
466	891	7771217463620761	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	4ea0fc324b815d8
294	980	7771217461915167	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	f4576ebb998764d7
254	991	7771217461515031	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	62b9078695bdf7a9
488	1000	7771217463857844	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	a51da225f72d8a59
478	1009	7771217463759265	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	10c2ae2c52d4280f
622	1109	7771217465201733	esp-idf/esp_rom/libesp_rom.a	76a5ded7b2ca549c
717	1134	7771217466141828	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	693b1cefdf5675af
740	1146	7771217466373061	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	436ab8398ae337cd
534	1156	7771217464318402	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	2e1da854db8e92a3
669	1165	7771217465651792	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	7d9848c03906a592
505	1174	7771217464014326	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	80620778d920c90f
752	1183	7771217466501862	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	b3d5472d26ef9a3
552	1192	7771217464479387	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	6fc297156240c2d6
775	1202	7771217466730705	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	548cd74f6dddaa83
582	1215	7771217464795810	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	3c045548febdeacf
762	1359	7771217466596716	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	d4c198ef93cb0627
852	1401	7771217467495180	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	c572c5c7d1787c64
891	1486	7771217467885889	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	dd68be7af830e54b
1147	1525	7771217470443312	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	fb1d783f1fc6999f
1000	1540	7771217468974285	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	958a6f0343d3a6ae
980	1548	7771217468778813	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	8691aaacde663ef1
1166	1559	7771217470625250	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	443b96efb7c6069c
1109	1567	7771217470058905	esp-idf/esp_common/libesp_common.a	16d17467a2c4e525
1184	1576	7771217470814447	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	531bac33cf71ed00
1174	1584	7771217470712162	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	7be12f9a9c15e345
1215	1592	7771217471123448	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	f088d255c3206d2d
879	1636	7771217467755615	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	73bad240e72038db
1156	1669	7771217470536660	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	8c5fa4f5a3433383
1401	1714	7771217472989752	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	56faded760bb5c6d
1135	1723	7771217470325734	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	28b9d620aa85dde0
1202	1731	7771217470988417	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	d45cd5d41e6f2780
1592	1828	7771217474895246	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	12889012e50b6dec
1487	1838	7771217473839533	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	4d7101eb17ce0b4e
1360	1847	7771217472570004	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	e6322ded129131d8
1636	1857	7771217475328386	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	8117b1abdd8ae5ad
1584	1865	7771217474817322	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	4ece997a782feb60
1193	1885	7771217470900401	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	18fb62ac85589c67
1525	1896	7771217474223084	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	3218a7a712f7c9f3
1670	1920	7771217475671565	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	4c46a4c58ed3afe3
1009	1929	7771217469054491	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	c8e18c3ff045ec9e
1541	1940	7771217474384982	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	dc5ff90409846236
992	1948	7771217468891737	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	61276d89f376900b
1549	1973	7771217474465388	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	514081a2fae92ff2
1731	1981	7771217476288032	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	391f82e2f04a00c5
1724	1989	7771217476202303	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	55e4485b45905110
1567	1997	7771217474647871	esp-idf/esp_hw_support/libesp_hw_support.a	a22e20a51d2bd2b0
1559	2016	7771217474571101	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	977179509d5045cf
1715	2027	7771217476120776	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	f9302f071792c865
1829	2058	7771217477262890	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	b57f8c2a254bd570
1885	2089	7771217477827315	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	c6232ff47fd254ba
1857	2117	7771217477542620	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	36284a70d4d93c65
1847	2128	7771217477437228	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	8b1a3e9c13088575
1838	2136	7771217477356981	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	728e213496b12ab9
1896	2144	7771217477936253	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	79a690756593a85d
1866	2154	7771217477631710	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	1d6d4d64b28f05f5
1929	2202	7771217478266817	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	d68e5dc1f90372f1
1921	2203	7771217478183998	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	622b247c7ed7b2f3
1949	2205	7771217478461516	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	cd88ef30ced30729
2016	2214	7771217479138541	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	1e186cd9fac3c7b3
1941	2220	7771217478381905	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	2f7eee66471fe57
1973	2243	7771217478696622	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	217868e2020e27fc
2027	2253	7771217479242595	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	c54abca9198becba
1981	2255	7771217478785736	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	f08f3d5dee8b8c0
1990	2257	7771217478875407	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	88fd07eda1bf1b0d
2145	2260	7771217481453377	project_elf_src_esp32s3.c	b8d0ff71e4b9b057
2145	2260	7771217481453377	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/project_elf_src_esp32s3.c	b8d0ff71e4b9b057
2089	2270	7771217479851576	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	f9f82b55bf0e4872
1997	2286	7771217478950717	esp-idf/esp_system/libesp_system.a	6ba3db5ebc7222d0
2058	2294	7771217479554756	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	2383bf602ff145a4
2128	2315	7771217480247391	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	cd9fe949a67de97
2118	2319	7771217480151625	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	1c61d55442d51011
2137	2341	7771217480344053	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	8fd2f941b1abe2d7
2260	2359	7771217481576435	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	f6defcaf21da4b68
2154	2387	7771217480514176	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	834482eb125c9057
2286	2454	7771217481838342	esp-idf/efuse/libefuse.a	b027f75ac9dc6d3a
1576	2534	7771217474734660	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	75d15f451b03cdd4
2454	2643	7771217483508051	esp-idf/bootloader_support/libbootloader_support.a	41abbb3847180a9a
2643	2762	7771217485395634	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	7704240e3fc65260
2762	2879	7771217486587831	esp-idf/spi_flash/libspi_flash.a	f2f1321b4133f01b
2879	3005	7771217487754263	esp-idf/hal/libhal.a	4ecd0b7d65881b5c
3005	3121	7771217489015152	esp-idf/micro-ecc/libmicro-ecc.a	564c426ed7c13bba
3121	3296	7771217490178370	esp-idf/soc/libsoc.a	be819c31531e0d15
3296	3418	7771217491934749	esp-idf/xtensa/libxtensa.a	1e98b69c813b3b67
3418	3533	7771217493148333	esp-idf/main/libmain.a	5d9eeaf9cc3081fe
3533	3747	7771217494299827	bootloader.elf	7788bcbde2f2077
3747	4020	7771217499111646	.bin_timestamp	d1e10164ef537244
3747	4020	7771217499111646	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/.bin_timestamp	d1e10164ef537244
4020	4120	7771217499173879	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
4020	4120	7771217499173879	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
44	270	7771218518471968	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
44	270	7771218518471968	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
45	184	7771221403547564	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
45	184	7771221403547564	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
