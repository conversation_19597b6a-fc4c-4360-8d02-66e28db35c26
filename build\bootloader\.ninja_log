# ninja log v6
41	215	7771200149491554	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	37737cafe2c97f36
70	229	7771200149792854	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	c9096435ad028d02
25	245	7771200149305303	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	56acb7dc8102ce57
15	254	7771200149237133	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	48d78e6f6eb7ef02
31	289	7771200149385494	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	a54cd58b4ea2d6e4
20	297	7771200149243150	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	92a334b9bdb7bea1
103	307	7771200150108450	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	3ac96857e3373b2f
64	319	7771200149722404	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	4ba88eaecf9cbf9d
77	333	7771200149853809	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	f3f9f5928d063257
58	342	7771200149659371	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	c653ae0c57ab84a7
47	357	7771200149549917	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	23dc2db1ff76a9dd
52	373	7771200149603697	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	874f2caf2cd773fa
84	387	7771200149917866	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	b91d81ad8ff19494
96	432	7771200150031268	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	cd48953879898f46
37	442	7771200149436947	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	5c8e6396075a08
127	498	7771200150354040	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	477ab82d7db0066
111	510	7771200150189968	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	abc04690478016b6
217	518	7771200151249436	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	99a439aca56bdc9f
118	527	7771200150267975	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	c6751480d71fadfd
229	547	7771200151372964	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	eaad3a2ccbfc33ab
298	666	7771200152061658	esp-idf/log/liblog.a	9fe540e68b3f479f
334	680	7771200152425032	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	683171ef346bab95
255	701	7771200151635292	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	7fd96f3ff434881b
358	734	7771200152667687	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	19abf20b7cd965a1
342	742	7771200152511871	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	2e47f15a06db94b3
307	768	7771200152150701	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	836ac219b043a2de
388	777	7771200152956322	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	53bdd91453e87a83
373	787	7771200152818825	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	c89d5483640779af
319	813	7771200152272020	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	514f687e88129b0
498	869	7771200154057021	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	c8bfccc8b916bbda
432	894	7771200153404586	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	644c46498ebf1419
246	903	7771200151546336	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	c30457ff6f9b86c
442	911	7771200153502265	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	32798785964082ec
290	919	7771200151979289	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	7a3ca850dc4da2cb
666	1064	7771200155734866	esp-idf/esp_rom/libesp_rom.a	76a5ded7b2ca549c
701	1073	7771200156089877	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	d796249b933e975e
519	1095	7771200154270817	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	c6621937814fa7c5
511	1105	7771200154193885	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	1aa5215a4174a2d1
680	1115	7771200155881560	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	af13ef1638fea9a5
743	1124	7771200156507644	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	42727b71597c78cb
527	1132	7771200154357258	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	4e297351cc77231f
735	1141	7771200156426731	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	24f1238b02a06e94
778	1166	7771200156861535	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	2d4067af1fbf7f95
787	1175	7771200156939173	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	856c1d8f2096e64e
547	1190	7771200154548520	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	b1664c91259eb143
768	1221	7771200156764538	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	e0cee0ac137233c
911	1266	7771200158195220	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	cae784e5d7c76c6a
870	1468	7771200157777278	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	a9ae8aec96126d51
1133	1514	7771200160419244	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	f2f85ccb24c72055
1175	1523	7771200160833949	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	4e97c2278b58d8bb
1125	1536	7771200160327028	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	e33040e72b74b4a9
1096	1545	7771200160046976	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	f3913d9bab7c5d24
894	1556	7771200158023873	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	484b4ddfe366e63c
1115	1565	7771200160238152	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	881621904d10cb14
1064	1576	7771200159728265	esp-idf/esp_common/libesp_common.a	16d17467a2c4e525
1221	1584	7771200161286208	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	d718002461e56cdc
1166	1596	7771200160748397	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	157492539690d61c
1266	1614	7771200161742198	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	7e27fac1e7667e4e
1105	1624	7771200160132688	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	c1f658ed435269c9
814	1655	7771200157224667	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	b20f3770ea22746e
920	1743	7771200158273015	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	b76744aaa1594fb5
1191	1759	7771200160981791	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	39fd6ddd1c9380df
1074	1781	7771200159815598	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	a10732585fe26e46
1566	1822	7771200164741539	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	b5d8af17db77cad3
1584	1839	7771200164930612	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	391abe225128d7dd
903	1847	7771200158111320	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	2440046b3d95391e
1557	1856	7771200164645410	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	5d750ebeb06e6fa
1469	1873	7771200163767982	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	e162e6ed7e0f73fb
1596	1897	7771200165050388	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	bdd751070ac11ed0
1141	1907	7771200160491103	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	ca551d57128d5fa4
1615	1916	7771200165232058	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	910f1b6ed00168fe
1624	1924	7771200165314448	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	5a34f9f8072c712
1514	1932	7771200164220480	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	e0ca6541ff1bfea6
1523	1941	7771200164311233	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	63e68359463fa0a9
1655	1950	7771200165642503	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	d40d595b1e93df09
1537	1962	7771200164445758	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	a3ac02af095db87d
1744	1978	7771200166521111	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	6243b5904ecf7de6
1576	2015	7771200164846914	esp-idf/esp_hw_support/libesp_hw_support.a	a22e20a51d2bd2b0
1781	2030	7771200166892263	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	fed7baaea5f7bcb0
1759	2044	7771200166677762	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	7109ee07c213c5a
1848	2052	7771200167565444	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	5e1652d250dade4e
1822	2092	7771200167309872	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	6df2d6564653a608
1840	2103	7771200167476252	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	c7e0500b0f74d4e4
1856	2111	7771200167646664	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	38a6ff504dcd4c6e
1916	2119	7771200168244647	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	dc36ac9fc165af81
1951	2156	7771200168583203	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	e61437d57f7ba354
1933	2160	7771200168415352	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	dd64e1e83a470c24
1873	2161	7771200167817284	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	1537b6eaf066cecf
1907	2163	7771200168158721	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	756d6d3a9fc59243
1962	2164	7771200168704972	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	e18ceea1d1364523
1898	2165	7771200168062047	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	a7eb2f97b50a4315
2103	2187	7771200170850818	project_elf_src_esp32s3.c	e164d18cb5a6e2d7
2103	2187	7771200170850818	C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/bootloader/project_elf_src_esp32s3.c	e164d18cb5a6e2d7
1942	2194	7771200168506242	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	71e93150bec2d615
1924	2196	7771200168319771	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	c9c84258f38d7f18
2030	2204	7771200169384744	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	4d56562b706c857
1978	2211	7771200168859632	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	bf09dfd5fc1a796d
2052	2213	7771200169606766	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	64803d488cd065e0
2015	2245	7771200169232019	esp-idf/esp_system/libesp_system.a	6ba3db5ebc7222d0
2045	2254	7771200169524488	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	4ca118193a013e72
2093	2274	7771200170010855	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	4dd891e8651ad438
2187	2278	7771200170951091	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	e004c84a763a82b6
2112	2316	7771200170202546	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	d52e1a4eba9f4abc
2245	2401	7771200171535093	esp-idf/efuse/libefuse.a	b027f75ac9dc6d3a
1546	2490	7771200164520021	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	87218052c12916ed
2401	2589	7771200173089634	esp-idf/bootloader_support/libbootloader_support.a	41abbb3847180a9a
2589	2710	7771200174974510	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	7704240e3fc65260
2710	2830	7771200176185738	esp-idf/spi_flash/libspi_flash.a	f2f1321b4133f01b
2830	2970	7771200177379984	esp-idf/hal/libhal.a	4ecd0b7d65881b5c
2970	3091	7771200178775862	esp-idf/micro-ecc/libmicro-ecc.a	564c426ed7c13bba
3091	3272	7771200179996578	esp-idf/soc/libsoc.a	be819c31531e0d15
3272	3413	7771200181803638	esp-idf/xtensa/libxtensa.a	1e98b69c813b3b67
3413	3533	7771200183211050	esp-idf/main/libmain.a	5d9eeaf9cc3081fe
3533	3758	7771200184414649	bootloader.elf	4cfe9e88cc114f0
3758	4047	7771200189461660	.bin_timestamp	b56506b1d61b0621
3758	4047	7771200189461660	C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/bootloader/.bin_timestamp	b56506b1d61b0621
4047	4141	7771200189548894	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	ce332485f9b2954e
4047	4141	7771200189548894	C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	ce332485f9b2954e
