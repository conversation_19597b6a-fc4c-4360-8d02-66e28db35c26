{"sources": [{"file": "C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/CMakeFiles/bootloader"}, {"file": "C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/CMakeFiles/bootloader.rule"}, {"file": "C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/CMakeFiles/bootloader-complete.rule"}, {"file": "C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "C:/Users/<USER>/Desktop/ESP32/Code/3.LVGL/project/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}